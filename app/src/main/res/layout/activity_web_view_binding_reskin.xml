<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
		xmlns:app="http://schemas.android.com/apk/res-auto"
		xmlns:tools="http://schemas.android.com/tools">

	<androidx.constraintlayout.widget.ConstraintLayout
			android:layout_width="match_parent"
			android:layout_height="match_parent"
			android:background="@drawable/background_confirmation_reskin"
			android:fitsSystemWindows="true">

		<androidx.coordinatorlayout.widget.CoordinatorLayout
				android:layout_width="0dp"
				android:layout_height="0dp"
				android:layout_marginTop="?attr/actionBarSize"
				android:background="@drawable/bg_card_rounded_ns"
				android:paddingTop="30dp"
				app:layout_constraintBottom_toBottomOf="parent"
				app:layout_constraintTop_toTopOf="parent"
				app:layout_constraintEnd_toEndOf="parent"
				app:layout_constraintStart_toStartOf="parent">

						<WebView
								android:id="@+id/wv_binding"
								android:layout_width="fill_parent"
								android:layout_height="match_parent"/>

						<ProgressBar
								android:id="@+id/progressBar1"
								style="?android:attr/progressBarStyleSmall"
								android:layout_width="@dimen/_50sdp"
								android:layout_height="@dimen/_50sdp"
								android:layout_gravity="center"
								android:indeterminate="false"
								app:layout_anchorGravity="center"
								android:visibility="gone"/>

		</androidx.coordinatorlayout.widget.CoordinatorLayout>
	</androidx.constraintlayout.widget.ConstraintLayout>

</layout>