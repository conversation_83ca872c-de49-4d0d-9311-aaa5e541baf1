<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    app:cardCornerRadius="8dp"
    tools:context=".ui.fragments.bukarekening.TabunganCardFragment">

    <androidx.cardview.widget.CardView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginStart="@dimen/space_x2"
        android:layout_marginEnd="@dimen/space_x2"
        android:layout_marginBottom="@dimen/space_x1_half"
        android:background="@color/white"
        app:cardCornerRadius="8dp">
        <ScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent">
            <LinearLayout
                android:id="@+id/ll_tabungan_card"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/space_x2"
                android:layout_marginTop="@dimen/space_x3"
                android:layout_marginEnd="@dimen/space_x2"
                android:layout_marginBottom="@dimen/space_x3"
                android:orientation="vertical">

                <ImageView
                    android:id="@+id/ivTabungan"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />


                <TextView
                    android:id="@+id/tvDesc"
                    style="@style/Body3SmallText.Medium.NeutralDark10"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:lineSpacingExtra="4sp"
                    android:layout_marginTop="@dimen/space_x2" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rvTabungan"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" />

                <TextView
                    android:id="@+id/tvFooter"
                    style="@style/Caption1SmallText.Medium.NeutralDark10"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:lineSpacingExtra="4sp"
                    android:layout_marginTop="@dimen/space_x2" />

                <LinearLayout
                    android:id="@+id/ll_detail_tabungan"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:background="@drawable/bg_neutral_light"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tvDetailTabungan"
                        style="@style/Body3SmallText.Bold.PrimaryBlue80"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center"
                        android:layout_marginStart="@dimen/space_x2"
                        android:layout_marginTop="@dimen/space_x1"
                        android:layout_marginBottom="@dimen/space_x1"
                        android:text="@string/detail_tabungan" />

                    <ImageView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="@dimen/space_x1"
                        android:layout_marginBottom="@dimen/space_x1"
                        android:src="@drawable/ic_arrow_right_blue" />
                </LinearLayout>


            </LinearLayout>
        </ScrollView>

    </androidx.cardview.widget.CardView>


</RelativeLayout>