package id.co.bri.brimo.ui.activities.dompetdigitalreskin;

import android.annotation.SuppressLint;
import android.app.Activity;
import android.content.Intent;
import android.graphics.Bitmap;
import android.net.Uri;
import android.net.http.SslError;
import android.os.Bundle;
import android.view.View;
import android.webkit.SslErrorHandler;
import android.webkit.WebResourceRequest;
import android.webkit.WebSettings;
import android.webkit.WebView;
import android.webkit.WebViewClient;

import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentTransaction;

import java.util.List;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IPresenter.saldodompetdigital.IWebViewBindingPresenter;
import id.co.bri.brimo.contract.IView.saldodompetdigital.IWebViewBindingView;
import id.co.bri.brimo.databinding.ActivityWebViewBindingBinding;
import id.co.bri.brimo.databinding.ActivityWebViewBindingReskinBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.apimodel.response.EwalletBindingActionsResponse;
import id.co.bri.brimo.models.apimodel.response.EwalletBindingResponse;
import id.co.bri.brimo.models.apimodel.response.EwalletProductResponse;
import id.co.bri.brimo.models.apimodel.response.EwalletUpdateStatusResponse;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity;
import id.co.bri.brimo.ui.activities.topuprevamp.TopUpRevampActivity;
import id.co.bri.brimo.ui.customviews.dialog.DialogExitCeria;

public class WebViewBindingReskinActivity extends NewSkinBaseActivity implements
        IWebViewBindingView,DialogExitCeria.DialogCeriaListener,
        View.OnClickListener {

    private ActivityWebViewBindingReskinBinding binding;

    private String code = "";
    private Boolean isUpdated = false;

    protected static final String TAG_RESPONSE = "response";
    protected static EwalletBindingResponse mEwalletBindingResponse;
    protected static EwalletProductResponse SelectedWallet;
    protected static String mCellphoneNumber;
    protected static String mTitle = "";
    private List<EwalletBindingActionsResponse> mEwalletBindingActionsResponses;
    private String urlBinding;

    @Inject
    IWebViewBindingPresenter<IWebViewBindingView> webViewBindingPresenter;

    public static void launchIntent(Activity caller, EwalletBindingResponse ewalletBindingResponse, EwalletProductResponse wallet,
                                    String cellphoneNumber) {
        mEwalletBindingResponse = ewalletBindingResponse;
        SelectedWallet = wallet;
        mCellphoneNumber = cellphoneNumber;

        if (mEwalletBindingResponse.getTitle() == null || mEwalletBindingResponse.getTitle().equalsIgnoreCase("")) mTitle = GeneralHelper.getString(R.string.gopay_title_bar);
        else mTitle = mEwalletBindingResponse.getTitle();

        Intent intent = new Intent(caller, WebViewBindingReskinActivity.class);
        caller.startActivityForResult(intent, Constant.REQ_WALLET_BINDING_SUCCESS);
    }

    @SuppressLint("SetJavaScriptEnabled")
    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityWebViewBindingReskinBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        mEwalletBindingActionsResponses = mEwalletBindingResponse.getActions();

        injectDependency();


        WebSettings webSetting = binding.wvBinding.getSettings();
        webSetting.setJavaScriptEnabled(true);
        webSetting.setDomStorageEnabled(true);

        urlBinding = "";
        if (mEwalletBindingActionsResponses != null){
            for (int i = 0; i < mEwalletBindingActionsResponses.size(); i++) {
                if (mEwalletBindingActionsResponses.get(i).getName().equalsIgnoreCase(GeneralHelper.getString(R.string.flagging_url_activation))){
                    urlBinding = mEwalletBindingActionsResponses.get(i).getUrl();
                }
            }
        }

        binding.wvBinding.loadUrl(urlBinding);
        binding.progressBar1.setVisibility(View.VISIBLE);
        binding.wvBinding.setWebViewClient(new WebViewClient(){
            @Override
            public void onPageStarted(WebView view, String url, Bitmap favicon) {
                if (!WebViewBindingReskinActivity.this.isFinishing()) {
                    binding.progressBar1.setVisibility(View.VISIBLE);
                }
            }


            @Override
            public void onPageFinished(WebView view, String url) {
                if (!WebViewBindingReskinActivity.this.isFinishing()) {
                    binding.progressBar1.setVisibility(View.GONE);
                    if (url.contains(Constant.BINDING_CODE_AUTH_CODE) && url.contains(Constant.BINDING_CODE_STATE) ) {
                        if (!isUpdated) {
                            Uri uri = Uri.parse(url);
                            String authCode = uri.getQueryParameter(Constant.BINDING_CODE_AUTH_CODE);
                            String state = uri.getQueryParameter(Constant.BINDING_CODE_STATE);
                            String statusSukses;
                            if (authCode == null || authCode.isEmpty() || state == null || state.isEmpty()) statusSukses = "0";
                            else statusSukses = "1";
                            webViewBindingPresenter.onSuccessGetCode(mEwalletBindingResponse.getBindingType(), statusSukses, "00", state, authCode);
                            isUpdated = true;
                        }
                    } else if (url.contains(Constant.BINDING_CODE_GOTO)) {
                        if (!isUpdated) {
                            Uri uri = Uri.parse(url);
                            code = uri.getQueryParameter(Constant.BINDING_CODE_GOTO);
                            String isSukses = uri.getQueryParameter(GeneralHelper.getString(R.string.flagging_url_success));
                            String statusSukses = "0";
                            if (isSukses != null) {
                                if (isSukses.equalsIgnoreCase("true")) {
                                    statusSukses = "1";
                                } else {
                                    statusSukses = "0";
                                }
                            }
                            String errorCode = uri.getQueryParameter(GeneralHelper.getString(R.string.flagging_url_error_code));
                            binding.wvBinding.setVisibility(View.GONE);
                            if (errorCode != null) {
                                webViewBindingPresenter.onSuccessGetCode(mEwalletBindingResponse.getBindingType(), statusSukses, errorCode, code, "");
                            } else {
                                webViewBindingPresenter.onSuccessGetCode(mEwalletBindingResponse.getBindingType(), statusSukses, "00", code, "");
                            }
                            isUpdated = true;
                        }
                    } else {
                        binding.wvBinding.setVisibility(View.VISIBLE);
                    }
                }
            }

            @Override
            public boolean shouldOverrideUrlLoading(WebView view, WebResourceRequest request) {
                binding.progressBar1.setVisibility(View.VISIBLE);
                return super.shouldOverrideUrlLoading(view, request);
            }

            @Override
            public void onReceivedSslError (WebView view, SslErrorHandler handler, SslError error) {
                if (!GeneralHelper.isProd()) handler.proceed();
            }
        });

        binding.wvBinding.loadUrl(urlBinding);
    }

    private void injectDependency() {
        getActivityComponent().inject(this);
        if (webViewBindingPresenter != null) {
            webViewBindingPresenter.setView(this);
            webViewBindingPresenter.setUrlUpdateStatusBinding(GeneralHelper.getString(R.string.url_ewallet_update_status));
            webViewBindingPresenter.start();
        }
    }

    @Override
    public void onClick(View v) {
        switch (v.getId()) {
            default:
                break;
        }
    }


    @Override
    public void onSuccessUpdateStatusBinding(EwalletUpdateStatusResponse ewalletUpdateStatusResponse) {
        SuccessBindingDompetDigitalReskinActivity.launchIntent(
                this,
                SelectedWallet,
                mEwalletBindingResponse,
                mCellphoneNumber
        );
        this.setResult(RESULT_OK);
        this.finish();
    }

    @Override
    public void onException12(String message) {
        Intent returnIntent = new Intent();

        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, message);

        this.setResult(RESULT_CANCELED, returnIntent);
        this.finish();
    }
    @Override
    public void onBackPressed() {
        this.finish();
    }

    @Override
    public void hideProgress() {
        if(!WebViewBindingReskinActivity.this.isFinishing()){

            GeneralHelper.dismissDialog();
        }
    }

    @Override
    public void onClickYes() {
        this.finish();
    }

    @Override
    public void onClickNo() {

    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (requestCode == Constant.CHROME_CUSTOM_TAB_REQUEST_CODE) {
//            exitWebApp();
        } else if (requestCode == Constant.REQ_WALLET_BINDING_SUCCESS) {
            if (resultCode == RESULT_OK) {
                // Wallet binding was successful, pass result back to FormDompetDigitalReskinActivity
                setResult(RESULT_OK, data);
                finish();
            }
        }
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        binding = null;
    }
}