package id.co.bri.brimo.ui.activities.dompetdigitalreskin

import android.app.Activity
import android.content.Intent
import android.graphics.Typeface
import android.os.Bundle
import android.os.SystemClock
import android.text.SpannableString
import android.text.Spanned
import android.text.style.StyleSpan
import android.view.View
import androidx.appcompat.widget.SearchView
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.SavedAdapterNs
import id.co.bri.brimo.adapters.HistoryDompetDigitalAdapterNs
import id.co.bri.brimo.contract.IView.dompetdigitalreskin.ISearchSavedHistoryDompetDigitalView
import id.co.bri.brimo.databinding.ActivitySearchSavedHistoryBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin
import id.co.bri.brimo.models.AccountModel
import id.co.bri.brimo.models.ParameterModel
import id.co.bri.brimo.models.apimodel.response.HistoryResponse
import id.co.bri.brimo.models.apimodel.response.SavedResponse
import id.co.bri.brimo.models.apimodel.response.dompetdigitalrevamp.InquiryDompetDigitalResponse
import id.co.bri.brimo.presenters.dompetdigitalreskin.ISearchSavedHistoryDompetDigitalPresenter
import id.co.bri.brimo.ui.activities.FormEditSavedReskinActivity
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimo.ui.fragments.UpdateSavedItemNsFragment
import id.co.bri.brimo.ui.fragments.bottomsheet.HapusConfirmationBottomSheetFragment
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralNewSkinFragment
import javax.inject.Inject
import kotlin.collections.mutableListOf

class SearchSavedHistoryDompetDigitalActivity : NewSkinBaseActivity(),
    ISearchSavedHistoryDompetDigitalView,
    SavedAdapterNs.ClickItem,
    HistoryDompetDigitalAdapterNs.ClickItem,
    UpdateSavedItemNsFragment.UpdateSavedInterface {

    private lateinit var binding: ActivitySearchSavedHistoryBinding
    private lateinit var savedAdapter: SavedAdapterNs
    private lateinit var historyAdapter: HistoryDompetDigitalAdapterNs
    private var savedResponses = mutableListOf<SavedResponse>()
    private var historyResponses = mutableListOf<HistoryResponse>()
    private lateinit var mListAccountModel: List<AccountModel>
    private var filteredSavedResponses = mutableListOf<SavedResponse>()
    private var filteredHistoryResponses = mutableListOf<HistoryResponse>()
    private var defaultIcon = 0
    private var isFromFastMenu = false
    private var lastClickTime = 0L
    private var currentSearchQuery = ""

    private var mNonimal = ""

    @Inject
    lateinit var presenter: ISearchSavedHistoryDompetDigitalPresenter<ISearchSavedHistoryDompetDigitalView>

    companion object {
        private const val TAG_SAVED_RESPONSES = "saved_responses"
        private const val TAG_HISTORY_RESPONSES = "history_responses"
        private const val TAG_DEFAULT_ICON = "default_icon"
        private const val TAG_FROM_FAST_MENU = "from_fast_menu"
        private const val TAG_ACCOUNT_LIST = "account_list"

        @JvmStatic
        fun launchIntent(
            caller: Activity,
            savedResponses: List<SavedResponse>,
            historyResponses: List<HistoryResponse>,
            listAccountModel: List<AccountModel>?,
            defaultIcon: Int,
            isFromFastMenu: Boolean
        ) {
            val intent = Intent(caller, SearchSavedHistoryDompetDigitalActivity::class.java)
            intent.putExtra(TAG_SAVED_RESPONSES, Gson().toJson(savedResponses))
            intent.putExtra(TAG_HISTORY_RESPONSES, Gson().toJson(historyResponses))
            intent.putExtra(TAG_DEFAULT_ICON, defaultIcon)
            intent.putExtra(TAG_FROM_FAST_MENU, isFromFastMenu)
            intent.putExtra(TAG_ACCOUNT_LIST, ArrayList(listAccountModel))
            caller.startActivityForResult(intent, Constant.REQ_SEARCH_SAVED_HISTORY)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivitySearchSavedHistoryBinding.inflate(layoutInflater)
        setContentView(binding.root)

        getIntentData()
        injectDependency()
        setupView()
        setupAdapter()
        setupSearchView()
    }

    private fun getIntentData() {
        intent?.let {
            val savedJson = it.getStringExtra(TAG_SAVED_RESPONSES)
            val historyJson = it.getStringExtra(TAG_HISTORY_RESPONSES)
            defaultIcon = it.getIntExtra(TAG_DEFAULT_ICON, 0)
            isFromFastMenu = it.getBooleanExtra(TAG_FROM_FAST_MENU, false)

            if (!savedJson.isNullOrEmpty()) {
                val savedType = object : TypeToken<List<SavedResponse>>() {}.type
                savedResponses = Gson().fromJson(savedJson, savedType) ?: mutableListOf()
            }

            if (!historyJson.isNullOrEmpty()) {
                val historyType = object : TypeToken<List<HistoryResponse>>() {}.type
                historyResponses = Gson().fromJson(historyJson, historyType) ?: mutableListOf()
            }

            @Suppress("DEPRECATION", "UNCHECKED_CAST")
            mListAccountModel = it.getSerializableExtra(TAG_ACCOUNT_LIST) as ArrayList<AccountModel>
        }
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this

        if (isFromFastMenu) {
            presenter.setUrlInquiry(GeneralHelper.getString(R.string.url_fm_inquiry_dompet_digital_v3))
            presenter.setUrlConfirm(GeneralHelper.getString(R.string.url_fm_confirmation_dompet_digital_v3))
            presenter.setUrlPayment(GeneralHelper.getString(R.string.url_fm_payment_dompet_digital_v3))
        } else {
            presenter.setUrlInquiry(GeneralHelper.getString(R.string.url_inquiry_dompet_digital_v3))
            presenter.setUrlConfirm(GeneralHelper.getString(R.string.url_confirmation_dompet_digital_v3))
            presenter.setUrlPayment(GeneralHelper.getString(R.string.url_payment_dompet_digital_v3))
        }
        presenter.start()
    }

    private fun setupView() {
        GeneralHelperNewSkin.setToolbar(
            this,binding.toolbar.toolbar,
            getString(R.string.ewallet),
        )

        // Initialize filtered lists as empty initially (blank search result)
        filteredSavedResponses.clear()
        filteredHistoryResponses.clear()

        updateEmptyState()
    }

    private fun setupAdapter() {
        // Setup saved adapter
        savedAdapter = SavedAdapterNs(
            this,
            filteredSavedResponses,
            this,
            defaultIcon,
            isFromFastMenu
        )
        binding.rvDaftarFavorit.layoutManager = LinearLayoutManager(this)
        binding.rvDaftarFavorit.adapter = savedAdapter

        // Setup history adapter
        historyAdapter = HistoryDompetDigitalAdapterNs(
            this,
            filteredHistoryResponses,
            this,
            defaultIcon,
            isFromFastMenu
        )
        binding.rvRiwayat.layoutManager = LinearLayoutManager(this)
        binding.rvRiwayat.adapter = historyAdapter
    }

    private fun setupSearchView() {
        // Request focus on SearchView when activity starts
        binding.searchView.post {
            binding.searchView.requestFocus()
        }

        // Handle focus change to manually update background
        binding.searchView.setOnQueryTextFocusChangeListener { _, hasFocus ->
            if (hasFocus) {
                binding.searchView.setBackgroundResource(R.drawable.bg_input_double_border_focused_ns)
            } else {
                binding.searchView.setBackgroundResource(R.drawable.bg_input_black_100_brimo_ns)
            }
        }

        binding.searchView.setOnQueryTextListener(object : SearchView.OnQueryTextListener {
            override fun onQueryTextSubmit(query: String?): Boolean {
                return false
            }

            override fun onQueryTextChange(newText: String?): Boolean {
                currentSearchQuery = newText ?: ""
                filterData(currentSearchQuery)
                return false
            }
        })
    }

    private fun filterData(query: String) {
        val lowerCaseQuery = query.lowercase()

        // Filter saved responses
        filteredSavedResponses.clear()
        if (query.isNotEmpty()) {
            savedResponses.forEach { saved ->
                if (saved.title?.lowercase()?.contains(lowerCaseQuery) == true) {
                    filteredSavedResponses.add(saved)
                }
            }
        }

        // Filter history responses
        filteredHistoryResponses.clear()
        if (query.isNotEmpty()) {
            historyResponses.forEach { history ->
                if (history.title?.lowercase()?.contains(lowerCaseQuery) == true) {
                    filteredHistoryResponses.add(history)
                }
            }
        }

        // Update adapters with search query for highlighting
        savedAdapter.setSearchQuery(query)
        historyAdapter.setSearchQuery(query)

        // Notify adapters
        savedAdapter.notifyDataSetChanged()
        historyAdapter.notifyDataSetChanged()

        // Update empty state
        updateEmptyState(query.isNotEmpty())
    }

    private fun updateEmptyState(isSearching: Boolean = false) {
        // Show/hide sections based on content and search state
        binding.contentFavorit.visibility = if (filteredSavedResponses.isNotEmpty()) View.VISIBLE else View.GONE
        binding.contentRiwayat.visibility = if (filteredHistoryResponses.isNotEmpty()) View.VISIBLE else View.GONE

        // Show/hide start search view based on whether user is searching
        if (isSearching) {
            // User has entered some characters, hide start search view
            binding.llStartSearch.visibility = View.GONE

            // Show no search results if searching and both lists are empty
            val hasAnyResults = filteredSavedResponses.isNotEmpty() || filteredHistoryResponses.isNotEmpty()
            if (!hasAnyResults) {
                binding.llNoDataSearchFound.visibility = View.VISIBLE
            } else {
                binding.llNoDataSearchFound.visibility = View.GONE
            }
        } else {
            // No query entered, show start search view and hide no data found
            binding.llStartSearch.visibility = View.VISIBLE
            binding.llNoDataSearchFound.visibility = View.GONE
        }
    }

    private fun updateSavedItemInLists(savedResponse: SavedResponse, position: Int, updateAction: (SavedResponse) -> Unit) {
        // Find and update item in original list
        val originalIndex = savedResponses.indexOfFirst { it.value == savedResponse.value }
        if (originalIndex != -1) {
            updateAction(savedResponses[originalIndex])
        }

        // Find and update item in filtered list
        val filteredIndex = filteredSavedResponses.indexOfFirst { it.value == savedResponse.value }
        if (filteredIndex != -1) {
            updateAction(filteredSavedResponses[filteredIndex])
            savedAdapter.notifyItemChanged(filteredIndex)
        }
    }

    private fun removeSavedItemFromLists(savedResponse: SavedResponse, position: Int) {
        // Remove from original list
        val originalIndex = savedResponses.indexOfFirst { it.value == savedResponse.value }
        if (originalIndex != -1) {
            savedResponses.removeAt(originalIndex)
        }

        // Remove from filtered list
        val filteredIndex = filteredSavedResponses.indexOfFirst { it.value == savedResponse.value }
        if (filteredIndex != -1) {
            filteredSavedResponses.removeAt(filteredIndex)
            savedAdapter.notifyItemRemoved(filteredIndex)
        }
    }

    // SavedAdapterNs.ClickItem implementations
    override fun onClickSavedItem(savedResponse: SavedResponse) {
        if (SystemClock.elapsedRealtime() - lastClickTime < 1000) return
        lastClickTime = SystemClock.elapsedRealtime()

        val s = savedResponse.value
        val strArr = s.split("\\|".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
        if (strArr.size > 2) {
            val ewalletCode = strArr[1]
            val corpCode = strArr[2]
            val purchaseNumber = strArr[3]
            presenter.getDataInquirySaved(isFromFastMenu, ewalletCode, corpCode, purchaseNumber)
        }
    }

    override fun onClickUpdateItem(savedResponse: SavedResponse, position: Int) {
        val updateSavedItemFragment = UpdateSavedItemNsFragment(savedResponse, this, position)
        updateSavedItemFragment.show(supportFragmentManager, "")
    }

    // HistoryDompetDigitalAdapterNs.ClickItem implementations
    override fun onClickHistoryItem(historyResponse: HistoryResponse) {
        if (SystemClock.elapsedRealtime() - lastClickTime < 1000) return
        lastClickTime = SystemClock.elapsedRealtime()

        val s = historyResponse.value
        val strArr = s.split("\\|".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
        if (strArr.size > 1) {
            val ewalletCode = strArr[0]
            val corpCode = strArr[1]
            val purchaseNumber = strArr[2]
            mNonimal = strArr[3]
            presenter.getDataInquirySaved(isFromFastMenu, ewalletCode, corpCode, purchaseNumber)
        }
    }

    // ISearchSavedHistoryDompetDigitalView implementations
    override fun onSuccessGetInquirySaved(
        inquiryDompetDigitalResponse: InquiryDompetDigitalResponse,
        urlConfirm: String,
        urlPayment: String,
        isFromFastMenu: Boolean,
        purchaseNumber: String
    ) {
        InquiryDompetDigitalReskinActivity.launchIntent(
            this,
            inquiryDompetDigitalResponse,
            urlConfirm,
            urlPayment,
            isFromFastMenu,
            mNonimal,
            purchaseNumber,
            mListAccountModel
        )
    }

    override fun onFailedGetInquirySaved(message: String) {
        showSnackbarErrorMessage(message, ALERT_ERROR, this, false)
    }

    override fun onSuccessUpdate(savedResponse: SavedResponse, position: Int, type: Int) {
        // Show success message
        val message = GeneralHelper.getString(R.array.type_option_desc_newskin, type)
        showSnackbarErrorMessage(message, ALERT_CONFIRM, this, false)

        // Update UI lists locally without refetching data
        when (type) {
            Constant.EditOptionNs.FAV -> {
                // Update favorite status in both original and filtered lists
                updateSavedItemInLists(savedResponse, position) { item ->
                    item.favorite = true
                }
            }
            Constant.EditOptionNs.NON_FAV -> {
                // Update favorite status in both original and filtered lists
                updateSavedItemInLists(savedResponse, position) { item ->
                    item.favorite = false
                }
            }
            Constant.EditOptionNs.HAPUS -> {
                // Remove item from both original and filtered lists
                removeSavedItemFromLists(savedResponse, position)
            }
            Constant.EditOptionNs.EDIT -> {
                // Item will be updated when returning from edit activity
                // No immediate action needed here
            }
        }

        // Update empty states after list modifications
        updateEmptyState(currentSearchQuery.isNotEmpty())
    }

    // UpdateSavedItemNsFragment.UpdateSavedInterface implementation
    override fun onUpdateItem(savedResponseItem: SavedResponse?, type: Int, position: Int) {
        if (type == Constant.EditOptionNs.FAV) {
            presenter.setUpdateItem(
                GeneralHelper.getString(R.string.url_wallet_favorite_saved_v3),
                savedResponseItem!!, position, type
            )
        }

        if (type == Constant.EditOptionNs.HAPUS) {
            hapusConfirmation(savedResponseItem!!, type, position)
        }

        if (type == Constant.EditOptionNs.EDIT) {
            FormEditSavedReskinActivity.launchIntent(
                this,
                savedResponseItem,
                position,
                getDefaultIconResource(),
                GeneralHelper.getString(R.string.url_wallet_update_nickname_v3),
                getString(R.string.jenis_e_wallet),
                getString(R.string.txt_nomor_hp)
            )
        }

        if (type == Constant.EditOptionNs.NON_FAV) {
            presenter.setUpdateItem(
                GeneralHelper.getString(R.string.url_wallet_remove_saved_v3),
                savedResponseItem!!, position, Constant.EditOptionNs.NON_FAV
            )
        }
    }

    override fun onException(message: String) {
        if (GeneralHelper.isContains(Constant.LIST_TYPE_GAGAL, message)) {
            GeneralHelper.showDialogGagalBack(this, message)
        } else {
            showSnackbarErrorMessage(message, ALERT_ERROR, this, false)
        }
    }

    override fun getDefaultIconResource(): Int {
        return 0
    }

    private fun hapusConfirmation(savedResponseItem: SavedResponse, type: Int, position: Int){
        showHapusConfirmationBottomSheet(savedResponseItem, type, position)
    }

    private fun showHapusConfirmationBottomSheet(savedResponseItem: SavedResponse, type: Int, position: Int) {
        var spannableString: SpannableString

        savedResponseItem.let { item ->
            // Get the template string
            val template = GeneralHelper.getString(R.string.desc_hapus_dialog_ns)
            val fullText = String.format(template, item.title)

            spannableString = SpannableString(fullText)

            // Find the position of the title in the full text
            val titleStart = fullText.indexOf(item.title)
            val titleEnd = titleStart + item.title.length

            if (titleStart >= 0) {
                spannableString.setSpan(
                    StyleSpan(Typeface.BOLD),
                    titleStart,
                    titleEnd,
                    Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
                )
            }
        }

        OpenBottomSheetGeneralNewSkinFragment.showDialogConfirmationWithStringFormat(
            fragmentManager = supportFragmentManager,
            imgDrawable = R.drawable.ic_sad_illustration,
            imgName = "ic_sad_illustration",
            titleTxt = GeneralHelper.getString(R.string.txt_hapus_favorit),
            subTitleTxt = spannableString,
            btnFirstFunction = {
                // Close the dialog
            },
            btnSecondFunction = {
                presenter.setUpdateItem(
                    GeneralHelper.getString(R.string.url_wallet_delete_saved_v3),
                    savedResponseItem, position, type
                )
            },
            isClickableOutside = true,
            firstBtnTxt = GeneralHelper.getString(R.string.batal),
            secondBtnTxt = GeneralHelper.getString(R.string.delete),
            showCloseButton = true,
            showPill = true
        )
    }

    fun setParameter(): ParameterModel {
        val parameterModel = ParameterModel()
        parameterModel.stringLabelTujuan = GeneralHelper.getString(R.string.nomor_tujuan)
        parameterModel.stringLabelNominal = GeneralHelper.getString(R.string.nominal_pembayaran)
        parameterModel.stringButtonSubmit = GeneralHelper.getString(R.string.top_up)
        parameterModel.stringLabelMinimum = GeneralHelper.getString(R.string.top_up)
        parameterModel.defaultIcon = 0
        return parameterModel
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)

        if (requestCode == Constant.REQ_EDIT_SAVED && data != null) {
            if (resultCode == RESULT_OK) {
                val newName = data.getStringExtra(Constant.TAG_TITLE)
                val position = data.getIntExtra(Constant.TAG_POSITION, 0)

                // Update the item name in both original and filtered lists
                if (newName != null && position >= 0 && position < savedResponses.size) {
                    savedResponses[position].title = newName

                    // Also update in filtered list if it exists
                    val filteredIndex = filteredSavedResponses.indexOfFirst { it.value == savedResponses[position].value }
                    if (filteredIndex != -1) {
                        filteredSavedResponses[filteredIndex].title = newName
                        savedAdapter.notifyItemChanged(filteredIndex)
                    }
                }

                // Show success message
                val message = GeneralHelper.getString(R.array.type_option_desc_newskin, Constant.EditOptionNs.EDIT)
                showSnackbarErrorMessage(message, ALERT_CONFIRM, this, false)
            }
        }
    }

    override fun onBackPressed() {
        // Set result to indicate that data should be refreshed in FormDompetDigitalReskinActivity
        setResult(RESULT_OK)
        super.onBackPressed()
    }
}
